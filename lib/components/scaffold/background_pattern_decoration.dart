import 'package:flutter/material.dart';

import '../../theme/colors.dart';

class BackgroundPatternDecoration extends BoxDecoration {
  BackgroundPatternDecoration.page()
    : super(
        image: DecorationImage(
          alignment: Alignment.topCenter,
          image: AssetImage('assets/images/page_background.png'),
          fit: BoxFit.fitWidth,
        ),
        color: AppColors.gray2,
      );

  BackgroundPatternDecoration.menu()
    : super(
        image: DecorationImage(
          alignment: Alignment.bottomCenter,
          image: AssetImage('assets/images/menu_background.png'),
          fit: BoxFit.fitWidth,
        ),
        color: AppColors.white,
      );
}
